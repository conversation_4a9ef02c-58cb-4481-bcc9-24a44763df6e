#!/bin/bash
# ~/.config/lvim/test_mcp.sh
# MCP 功能快速测试脚本

set -euo pipefail

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 测试计数器
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# 运行测试
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    log_info "测试 $TESTS_TOTAL: $test_name"
    
    if eval "$test_command" >/dev/null 2>&1; then
        log_success "✅ $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "❌ $test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# 测试系统依赖
test_system_dependencies() {
    log_info "=== 测试系统依赖 ==="
    
    run_test "Node.js 可用性" "command -v node"
    run_test "npm 可用性" "command -v npm"
    run_test "npx 可用性" "command -v npx"
    
    if command -v node >/dev/null 2>&1; then
        local node_version=$(node --version)
        log_info "Node.js 版本: $node_version"
    fi
    
    if command -v npm >/dev/null 2>&1; then
        local npm_version=$(npm --version)
        log_info "npm 版本: $npm_version"
    fi
}

# 测试npm配置
test_npm_configuration() {
    log_info "=== 测试 npm 配置 ==="
    
    run_test "npm 全局目录存在" "[ -d ~/.npm-global ]"
    run_test "npm prefix 配置正确" "npm config get prefix | grep -q npm-global"
    run_test "npm 镜像源配置" "npm config get registry | grep -q npmmirror"
    
    local npm_prefix=$(npm config get prefix 2>/dev/null || echo "未配置")
    local npm_registry=$(npm config get registry 2>/dev/null || echo "未配置")
    
    log_info "npm prefix: $npm_prefix"
    log_info "npm registry: $npm_registry"
}

# 测试PATH配置
test_path_configuration() {
    log_info "=== 测试 PATH 配置 ==="
    
    export PATH=~/.npm-global/bin:$PATH
    
    run_test "npm 全局 bin 目录在 PATH 中" "echo \$PATH | grep -q npm-global"
    
    log_info "当前 PATH: $PATH"
}

# 测试MCP服务器安装
test_mcp_servers_installation() {
    log_info "=== 测试 MCP 服务器安装 ==="
    
    export PATH=~/.npm-global/bin:$PATH
    
    local servers=(
        "@modelcontextprotocol/server-filesystem"
        "@modelcontextprotocol/server-github"
        "@modelcontextprotocol/server-sequential-thinking"
    )
    
    for server in "${servers[@]}"; do
        run_test "$server 已安装" "npm list -g $server"
    done
}

# 测试MCP服务器可执行性
test_mcp_servers_execution() {
    log_info "=== 测试 MCP 服务器可执行性 ==="
    
    export PATH=~/.npm-global/bin:$PATH
    
    # 创建临时测试目录
    local test_dir="/tmp/mcp_test_$$"
    mkdir -p "$test_dir"
    
    # 测试文件系统服务器（MCP服务器通过stdio运行，我们只测试启动）
    run_test "文件系统服务器可执行" "timeout 2 bash -c 'echo | npx @modelcontextprotocol/server-filesystem $test_dir 2>/dev/null'"

    # 测试GitHub服务器
    run_test "GitHub服务器可执行" "timeout 2 bash -c 'echo | npx @modelcontextprotocol/server-github 2>/dev/null'"

    # 测试序列思考服务器
    run_test "序列思考服务器可执行" "timeout 2 bash -c 'echo | npx @modelcontextprotocol/server-sequential-thinking 2>/dev/null'"
    
    # 清理测试目录
    rm -rf "$test_dir"
}

# 测试LunarVim配置
test_lunarvim_configuration() {
    log_info "=== 测试 LunarVim 配置 ==="
    
    run_test "MCP 配置文件存在" "[ -f lua/user/ai/mcp.lua ]"
    run_test "MCP 诊断工具存在" "[ -f lua/user/ai/mcp_diagnostics.lua ]"
    run_test "插件配置包含 mcphub" "grep -q mcphub lua/user/plugins.lua"
    
    # 测试Lua语法
    if command -v luac >/dev/null 2>&1; then
        run_test "MCP 配置语法正确" "luac -p lua/user/ai/mcp.lua"
        run_test "MCP 诊断工具语法正确" "luac -p lua/user/ai/mcp_diagnostics.lua"
        run_test "插件配置语法正确" "luac -p lua/user/plugins.lua"
    else
        log_warning "luac 不可用，跳过语法检查"
    fi
}

# 测试环境变量
test_environment_variables() {
    log_info "=== 测试环境变量 ==="
    
    # 检查可选的环境变量
    if [ -n "${GITHUB_TOKEN:-}" ]; then
        log_success "✅ GITHUB_TOKEN 已设置"
    else
        log_warning "⚠️ GITHUB_TOKEN 未设置（可选）"
    fi
    
    if [ -n "${OPENAI_API_KEY:-}" ]; then
        log_success "✅ OPENAI_API_KEY 已设置"
    else
        log_warning "⚠️ OPENAI_API_KEY 未设置（可选）"
    fi
    
    if [ -n "${ANTHROPIC_API_KEY:-}" ]; then
        log_success "✅ ANTHROPIC_API_KEY 已设置"
    else
        log_warning "⚠️ ANTHROPIC_API_KEY 未设置（可选）"
    fi
}

# 生成测试报告
generate_test_report() {
    echo
    log_info "=== 测试报告 ==="
    echo "总测试数: $TESTS_TOTAL"
    echo "通过: $TESTS_PASSED"
    echo "失败: $TESTS_FAILED"
    echo "成功率: $(( TESTS_PASSED * 100 / TESTS_TOTAL ))%"
    echo
    
    if [ $TESTS_FAILED -eq 0 ]; then
        log_success "🎉 所有测试通过！MCP 配置完全正常。"
        echo
        log_info "下一步操作:"
        echo "1. 重启 LunarVim: lvim"
        echo "2. 运行 MCP 诊断: :MCPDiagnostics"
        echo "3. 测试 AI 功能: ,Am (MCP Hub)"
        echo
    else
        log_error "❌ 有 $TESTS_FAILED 个测试失败。"
        echo
        log_info "故障排除建议:"
        echo "1. 运行自动修复: :MCPFix"
        echo "2. 重新安装: ./install_mcp.sh"
        echo "3. 查看详细指南: cat MCP_TROUBLESHOOTING.md"
        echo
    fi
}

# 主函数
main() {
    log_info "开始 MCP 功能测试..."
    echo "=" * 60
    
    test_system_dependencies
    echo
    
    test_npm_configuration
    echo
    
    test_path_configuration
    echo
    
    test_mcp_servers_installation
    echo
    
    test_mcp_servers_execution
    echo
    
    test_lunarvim_configuration
    echo
    
    test_environment_variables
    echo
    
    generate_test_report
}

# 运行主函数
main "$@"
