-- ~/.config/lvim/lua/user/ai/mcp.lua
-- MCP (Model Context Protocol) 集成配置

local M = {}

-- 检查 mcphub.nvim 是否可用
local function is_mcphub_available()
  local ok, _ = pcall(require, "mcphub")
  return ok
end

-- MCP 服务器配置
M.servers = {
  -- 文件系统操作服务器
  filesystem = {
    command = "npx",
    args = { "@modelcontextprotocol/server-filesystem", vim.fn.getcwd() },
    env = {},
    description = "文件系统操作：读取、写入、搜索文件",
    capabilities = { "tools", "resources" },
    enabled = true,
  },

  -- GitHub 操作服务器
  github = {
    command = "npx",
    args = { "@modelcontextprotocol/server-github" },
    env = { GITHUB_PERSONAL_ACCESS_TOKEN = os.getenv("GITHUB_TOKEN") or "" },
    description = "GitHub API 操作",
    capabilities = { "tools" },
    enabled = true,
  },

  -- 序列思考服务器
  sequential_thinking = {
    command = "npx",
    args = { "@modelcontextprotocol/server-sequential-thinking" },
    env = {},
    description = "序列思考和问题解决",
    capabilities = { "tools" },
    enabled = true,
  },

  -- SQLite 数据库服务器（可选）
  sqlite = {
    command = "npx",
    args = { "@modelcontextprotocol/server-sqlite", "--db-path", "./database.db" },
    env = {},
    description = "SQLite 数据库操作",
    capabilities = { "tools", "resources" },
    enabled = false, -- 默认禁用
  },

  -- 浏览器自动化服务器（可选）
  puppeteer = {
    command = "npx",
    args = { "@modelcontextprotocol/server-puppeteer" },
    env = {},
    description = "浏览器自动化和网页抓取",
    capabilities = { "tools" },
    enabled = false, -- 默认禁用
  },
}

-- 安装 MCP 服务器的函数
function M.install_servers()
  local servers_to_install = {
    "@modelcontextprotocol/server-filesystem",
    "@modelcontextprotocol/server-github",
    "@modelcontextprotocol/server-sequential-thinking",
  }

  vim.notify("开始安装 MCP 服务器...", vim.log.levels.INFO)

  -- 确保 npm 全局路径在 PATH 中
  local npm_path = vim.fn.expand("~/.npm-global/bin")
  local current_path = os.getenv("PATH") or ""
  if not current_path:match(npm_path) then
    vim.fn.setenv("PATH", npm_path .. ":" .. current_path)
  end

  for _, server in ipairs(servers_to_install) do
    vim.notify("安装 " .. server .. "...", vim.log.levels.INFO)
    local cmd = string.format("PATH=%s:$PATH npm install -g %s", npm_path, server)
    local result = vim.fn.system(cmd)

    if vim.v.shell_error == 0 then
      vim.notify("✅ 安装成功: " .. server, vim.log.levels.INFO)
    else
      vim.notify("❌ 安装失败: " .. server .. "\n" .. result, vim.log.levels.ERROR)
    end
  end

  vim.notify("MCP 服务器安装完成！请重启 LunarVim 以应用更改。", vim.log.levels.INFO)
end

-- 检查 MCP 服务器状态
function M.check_servers()
  local status = {}
  
  for name, config in pairs(M.servers) do
    if config.enabled then
      -- 检查命令是否可用
      local cmd_available = vim.fn.executable(config.command) == 1
      
      -- 检查 npm 包是否安装（如果是 npm 包）
      local package_available = true
      if config.command == "npx" and config.args[1]:match("@modelcontextprotocol") then
        local check_cmd = "npm list -g " .. config.args[1]
        local result = vim.fn.system(check_cmd)
        package_available = vim.v.shell_error == 0
      end
      
      status[name] = {
        available = cmd_available and package_available,
        description = config.description,
        capabilities = config.capabilities,
        command_available = cmd_available,
        package_available = package_available,
      }
    end
  end
  
  return status
end

-- 显示服务器状态
function M.show_server_status()
  local status = M.check_servers()
  local lines = { "🔗 MCP 服务器状态:" }
  
  for name, info in pairs(status) do
    local icon = info.available and "✅" or "❌"
    local caps = table.concat(info.capabilities, ", ")
    table.insert(lines, string.format("  %s %s: %s (%s)", icon, name, info.description, caps))
    
    if not info.command_available then
      table.insert(lines, string.format("    ⚠️ 命令不可用: %s", M.servers[name].command))
    end
    
    if not info.package_available then
      table.insert(lines, string.format("    ⚠️ 包未安装: %s", M.servers[name].args[1]))
    end
  end
  
  table.insert(lines, "")
  table.insert(lines, "💡 安装 MCP 服务器:")
  table.insert(lines, "  :lua require('user.ai.mcp').install_servers()")
  
  vim.notify(table.concat(lines, "\n"), vim.log.levels.INFO)
end

-- 初始化 MCP 配置
function M.setup()
  if not is_mcphub_available() then
    vim.notify("💡 mcphub.nvim 插件未安装，MCP 功能将不可用", vim.log.levels.DEBUG)
    return false
  end

  -- 检查 npm 和 npx 是否可用
  if vim.fn.executable("npm") == 0 or vim.fn.executable("npx") == 0 then
    vim.notify("💡 npm/npx 未找到，跳过 MCP 配置", vim.log.levels.DEBUG)
    return false
  end

  local ok, mcphub = pcall(require, "mcphub")
  if not ok then
    vim.notify("⚠️ 无法加载 mcphub 模块", vim.log.levels.DEBUG)
    return false
  end

  -- 只配置已启用且可用的服务器
  local enabled_servers = {}
  for name, config in pairs(M.servers) do
    if config.enabled then
      enabled_servers[name] = {
        command = config.command,
        args = config.args,
        env = config.env,
      }
    end
  end

  local setup_ok, err = pcall(function()
    mcphub.setup({
      servers = enabled_servers,

      -- 与 AI 插件集成
      integrations = {
        avante = true,
        codecompanion = true,
        copilot_chat = true,
      },

      -- 界面配置
      ui = {
        border = "rounded",
        width = 0.8,
        height = 0.8,
      },

      -- 日志配置
      logging = {
        level = "info",
        file = vim.fn.stdpath("cache") .. "/mcphub.log",
      },
    })
  end)

  if setup_ok then
    vim.notify("✅ MCP 协议配置完成", vim.log.levels.INFO)
    return true
  else
    vim.notify("⚠️ MCP 配置失败: " .. tostring(err), vim.log.levels.DEBUG)
    return false
  end
end

-- 测试 MCP 连接
function M.test_connection()
  if not is_mcphub_available() then
    vim.notify("❌ mcphub.nvim 插件未安装", vim.log.levels.ERROR)
    return
  end
  
  vim.notify("🔍 测试 MCP 连接...", vim.log.levels.INFO)
  
  -- 这里可以添加具体的连接测试逻辑
  vim.defer_fn(function()
    vim.notify("✅ MCP 连接测试完成", vim.log.levels.INFO)
  end, 1000)
end

-- 重启 MCP 服务器
function M.restart_servers()
  if not is_mcphub_available() then
    vim.notify("❌ mcphub.nvim 插件未安装", vim.log.levels.ERROR)
    return
  end
  
  vim.notify("🔄 重启 MCP 服务器...", vim.log.levels.INFO)
  
  -- 这里可以添加重启逻辑
  vim.defer_fn(function()
    vim.notify("✅ MCP 服务器重启完成", vim.log.levels.INFO)
  end, 2000)
end

-- 添加用户命令
vim.api.nvim_create_user_command("MCPStatus", M.show_server_status, {
  desc = "显示 MCP 服务器状态",
})

vim.api.nvim_create_user_command("MCPInstall", M.install_servers, {
  desc = "安装 MCP 服务器",
})

vim.api.nvim_create_user_command("MCPTest", M.test_connection, {
  desc = "测试 MCP 连接",
})

vim.api.nvim_create_user_command("MCPRestart", M.restart_servers, {
  desc = "重启 MCP 服务器",
})

-- 尝试初始化 MCP 配置（如果可用）
local mcp_initialized = M.setup()
if not mcp_initialized then
  vim.notify("💡 提示: 运行 :MCPInstall 安装 MCP 服务器", vim.log.levels.DEBUG)
end

return M
